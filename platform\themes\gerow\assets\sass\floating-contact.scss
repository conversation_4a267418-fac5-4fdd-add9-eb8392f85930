/**
 * Floating Contact Widget Styles
 */

.floating-contact-widget {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .floating-contact-item {
        position: relative;
        
        .contact-link {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            color: white;
            text-decoration: none;
            font-size: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            
            &:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                color: white;
                text-decoration: none;
            }
        }

        .contact-tooltip {
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;

            &::after {
                content: '';
                position: absolute;
                left: 100%;
                top: 50%;
                transform: translateY(-50%);
                border: 5px solid transparent;
                border-left-color: rgba(0, 0, 0, 0.8);
            }

            .tooltip-label {
                display: block;
                font-weight: 600;
                margin-bottom: 2px;
            }

            .tooltip-value {
                display: block;
                font-size: 11px;
                opacity: 0.9;
            }
        }

        &:hover .contact-tooltip {
            opacity: 1;
            visibility: visible;
            right: 65px;
        }
    }

    // Phone Contact
    .phone-contact .contact-link {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        
        &:hover {
            background: linear-gradient(135deg, #f7931e 0%, #ff6b35 100%);
        }
    }

    // Zalo Contact
    .zalo-contact .contact-link {
        background: linear-gradient(135deg, #0068ff 0%, #0084ff 100%);

        &:hover {
            background: linear-gradient(135deg, #0084ff 0%, #0068ff 100%);
        }

        // Custom Zalo icon if FontAwesome doesn't have it
        .fab.fa-zalo::before {
            content: "Z";
            font-family: Arial, sans-serif;
            font-weight: bold;
            font-style: normal;
        }
    }

    // Viber Contact
    .viber-contact .contact-link {
        background: linear-gradient(135deg, #665cac 0%, #7c4dff 100%);
        
        &:hover {
            background: linear-gradient(135deg, #7c4dff 0%, #665cac 100%);
        }
    }

    // Facebook Contact
    .facebook-contact .contact-link {
        background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
        
        &:hover {
            background: linear-gradient(135deg, #42a5f5 0%, #1877f2 100%);
        }
    }

    // TikTok Contact
    .tiktok-contact .contact-link {
        background: linear-gradient(135deg, #000000 0%, #ff0050 50%, #00f2ea 100%);
        
        &:hover {
            background: linear-gradient(135deg, #ff0050 0%, #00f2ea 50%, #000000 100%);
        }
    }

    // YouTube Contact
    .youtube-contact .contact-link {
        background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
        
        &:hover {
            background: linear-gradient(135deg, #cc0000 0%, #ff0000 100%);
        }
    }

    // Back to Top
    .back-to-top .contact-link {
        background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
        
        &:hover {
            background: linear-gradient(135deg, #c44569 0%, #ff6b9d 100%);
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .floating-contact-widget {
        right: 15px;
        gap: 6px;

        .floating-contact-item {
            .contact-link {
                width: 45px;
                height: 45px;
                font-size: 18px;
            }

            .contact-tooltip {
                right: 55px;
                padding: 6px 10px;
                font-size: 11px;

                .tooltip-value {
                    font-size: 10px;
                }
            }

            &:hover .contact-tooltip {
                right: 60px;
            }
        }
    }
}

@media (max-width: 480px) {
    .floating-contact-widget {
        right: 10px;
        gap: 5px;

        .floating-contact-item {
            .contact-link {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .contact-tooltip {
                display: none; // Hide tooltips on very small screens
            }
        }
    }
}

// Animation for widget entrance
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px) translateY(-50%);
    }
    to {
        opacity: 1;
        transform: translateX(0) translateY(-50%);
    }
}

.floating-contact-widget {
    animation: slideInRight 0.6s ease-out;
}

// Pulse animation for phone button
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
    }
}

.phone-contact .contact-link {
    animation: pulse 2s infinite;
}

// Hide widget when printing
@media print {
    .floating-contact-widget {
        display: none !important;
    }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
    .floating-contact-widget {
        .floating-contact-item {
            .contact-tooltip {
                background: rgba(255, 255, 255, 0.9);
                color: #333;

                &::after {
                    border-left-color: rgba(255, 255, 255, 0.9);
                }
            }
        }
    }
}
