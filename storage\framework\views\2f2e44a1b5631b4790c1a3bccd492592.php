<?php if(theme_option('theme_breadcrumb_enabled', 1) && Theme::get('breadcrumbEnabled', true)): ?>
    <?php
        $bgImage = Theme::get('breadcrumbBackgroundImage') ?: theme_option('breadcrumb_background');
        $breadcrumbBackgroundImageOverlayEnabled = Theme::get('breadcrumbBackgroundImageOverlayEnabled') ?: theme_option('breadcrumb_background_overlay_enabled', 'yes');
        $firstShapeImage = theme_option('breadcrumb_first_shape_image');
        $secondShapeImage = theme_option('breadcrumb_second_shape_image');
    ?>

    <section
        class="breadcrumb-area breadcrumb-bg <?php if($breadcrumbBackgroundImageOverlayEnabled === 'no'): ?> breadcrumb-bg-transparent <?php endif; ?>"
        <?php if($bgImage): ?>
            data-background="<?php echo e(RvMedia::getImageUrl($bgImage)); ?>"
        style="background-image: url(<?php echo e(RvMedia::getImageUrl($bgImage)); ?>);"
        <?php endif; ?>
    >
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="breadcrumb-content">
                        <?php if($pageTitle = Theme::get('pageTitle')): ?>
                            <h2 class="title"><?php echo BaseHelper::clean($pageTitle); ?></h2>
                        <?php endif; ?>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <?php $__currentLoopData = Theme::breadcrumb()->getCrumbs(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $crumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if(!$loop->last): ?>
                                        <li class="breadcrumb-item"><a href="<?php echo e($crumb['url']); ?>"><?php echo e($crumb['label']); ?></a>
                                        </li>
                                    <?php else: ?>
                                        <li
                                            class="breadcrumb-item active"
                                            aria-current="page"
                                        ><?php echo e($crumb['label']); ?></li>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <?php if($firstShapeImage || $secondShapeImage): ?>
            <div class="breadcrumb-shape-wrap">
                <?php if($firstShapeImage): ?>
                    <?php echo e(RvMedia::image($firstShapeImage, 'shape')); ?>

                <?php endif; ?>

                <?php if($secondShapeImage): ?>
                    <?php echo e(RvMedia::image($secondShapeImage, 'shape')); ?>

                <?php endif; ?>
            </div>
        <?php endif; ?>
    </section>
<?php endif; ?>
<?php /**PATH C:\laragon\www\dtc-business-company\platform\themes/gerow/partials/breadcrumbs.blade.php ENDPATH**/ ?>