@switch(Theme::get('preFooterSidebarStyle'))
    @case('style-1')
        {!! dynamic_sidebar('pre_footer_sidebar') !!}
    @break

    @case('style-2')
        {!! dynamic_sidebar('pre_footer_sidebar_1') !!}
    @break
@endswitch

<footer>
    <div class="footer-area-two footer-bg-two footer-style"
         @if ($bgFooter = Theme::get('footerBackgroundImage'))
             data-background="{{ RvMedia::getImageUrl($bgFooter) }}"
         @elseif ($bgFooter = theme_option('footer_background_image'))
             data-background="{{ RvMedia::getImageUrl($bgFooter) }}"
         @endif

         style="
            background-color: {{ Theme::get('footerBackgroundColor') ?: theme_option('footer_background_color', '#F8F8F8') }};
            --footer-text-color: {{ Theme::get('footerTextColor') ?: theme_option('footer_text_color', '#000000') }};
            --footer-text-muted-color: {{ Theme::get('footerTextMutedColor') ?: theme_option('footer_text_muted_color', '#777777') }};
            --footer-border-color: {{ Theme::get('footerBorderColor') ?: theme_option('footer_border_color', '#E0E0E0') }};
        "
    >
        <div class="footer-top-two">
            <div class="container">
                <div class="row">
                    @if (Theme::get('footerStyle', 'default') == 'default')
                        {!! dynamic_sidebar('footer_sidebar') !!}
                    @else
                        {!! dynamic_sidebar('footer_sidebar_style_1') !!}
                    @endif
                </div>
            </div>
        </div>

        {{-- Google Map Section --}}
        @if (theme_option('google_map_enabled', true))
            <div class="google-map-section">
                <div class="container-fluid p-0">
                    <div class="contact-map" style="height: {{ theme_option('google_map_height', '400') }}px;">
                        @if (theme_option('google_map_address'))
                            <iframe
                                src="https://www.google.com/maps?q={{ urlencode(theme_option('google_map_address')) }}&output=embed"
                                style="width: 100%; height: 100%; border: 0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                                title="Google Maps - {{ theme_option('google_map_address') }}">
                            </iframe>
                        @else
                            <div class="map-placeholder d-flex align-items-center justify-content-center"
                                 style="width: 100%; height: 100%; background-color: #f8f9fa; color: #6c757d;">
                                <div class="text-center">
                                    <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                                    <p class="mb-0">{{ __('Google Map will be displayed here') }}</p>
                                    <small>{{ __('Please enter your address in Theme Options') }}</small>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        @if (Theme::get('bottomFooterStyle', 'default') == 'default')
            {!! dynamic_sidebar('footer_bottom') !!}
        @else
            <div class="footer-bottom container">
                <div class="row align-items-center">
                    {!! dynamic_sidebar('footer_bottom_style_1') !!}
                </div>
            </div>
        @endif
    </div>
</footer>

{!! Theme::footer() !!}
