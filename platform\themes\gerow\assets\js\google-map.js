/**
 * Google Map Integration for Footer
 */

class GoogleMapFooter {
    constructor() {
        this.mapElement = document.getElementById('google-map');
        this.map = null;
        this.marker = null;
        this.init();
    }

    init() {
        if (!this.mapElement) {
            return;
        }

        // Check if Google Maps API is loaded
        if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
            console.warn('Google Maps API is not loaded');
            return;
        }

        this.initMap();
    }

    initMap() {
        // Get map configuration from data attributes or theme options
        const lat = parseFloat(this.mapElement.dataset.lat || window.themeOptions?.googleMapLatitude || 21.0285);
        const lng = parseFloat(this.mapElement.dataset.lng || window.themeOptions?.googleMapLongitude || 105.8542);
        const zoom = parseInt(this.mapElement.dataset.zoom || window.themeOptions?.googleMapZoom || 15);
        const mapType = this.mapElement.dataset.mapType || window.themeOptions?.googleMapType || 'roadmap';

        // Map options
        const mapOptions = {
            center: { lat: lat, lng: lng },
            zoom: zoom,
            mapTypeId: google.maps.MapTypeId[mapType.toUpperCase()],
            scrollwheel: false,
            navigationControl: false,
            mapTypeControl: false,
            scaleControl: false,
            draggable: true,
            styles: this.getMapStyles()
        };

        // Create map
        this.map = new google.maps.Map(this.mapElement, mapOptions);

        // Add marker
        this.addMarker(lat, lng);

        // Add info window if content is provided
        this.addInfoWindow();
    }

    addMarker(lat, lng) {
        const markerOptions = {
            position: { lat: lat, lng: lng },
            map: this.map,
            title: window.themeOptions?.googleMapMarkerTitle || 'Our Location',
            animation: google.maps.Animation.DROP
        };

        // Custom marker icon if provided
        if (window.themeOptions?.googleMapMarkerIcon) {
            markerOptions.icon = {
                url: window.themeOptions.googleMapMarkerIcon,
                scaledSize: new google.maps.Size(40, 40)
            };
        }

        this.marker = new google.maps.Marker(markerOptions);
    }

    addInfoWindow() {
        if (!window.themeOptions?.googleMapInfoWindow) {
            return;
        }

        const infoWindow = new google.maps.InfoWindow({
            content: window.themeOptions.googleMapInfoWindow
        });

        this.marker.addListener('click', () => {
            infoWindow.open(this.map, this.marker);
        });

        // Auto open info window if configured
        if (window.themeOptions?.googleMapAutoOpenInfo) {
            infoWindow.open(this.map, this.marker);
        }
    }

    getMapStyles() {
        // Default map styles - can be customized
        if (window.themeOptions?.googleMapCustomStyles) {
            try {
                return JSON.parse(window.themeOptions.googleMapCustomStyles);
            } catch (e) {
                console.warn('Invalid Google Map custom styles JSON');
            }
        }

        // Return default styles or empty array
        return [];
    }
}

// Initialize Google Map when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('google-map')) {
        // Wait for Google Maps API to load
        if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
            new GoogleMapFooter();
        } else {
            // Retry after a short delay
            setTimeout(() => {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    new GoogleMapFooter();
                }
            }, 1000);
        }
    }
});

// Global function to initialize map (called by Google Maps API callback)
window.initGoogleMapFooter = function() {
    new GoogleMapFooter();
};
