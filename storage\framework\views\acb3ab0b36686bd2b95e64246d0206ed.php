<?php switch(Theme::get('preFooterSidebarStyle')):
    case ('style-1'): ?>
        <?php echo dynamic_sidebar('pre_footer_sidebar'); ?>

    <?php break; ?>

    <?php case ('style-2'): ?>
        <?php echo dynamic_sidebar('pre_footer_sidebar_1'); ?>

    <?php break; ?>
<?php endswitch; ?>

<footer>
    <div class="footer-area-two footer-bg-two footer-style"
         <?php if($bgFooter = Theme::get('footerBackgroundImage')): ?>
             data-background="<?php echo e(RvMedia::getImageUrl($bgFooter)); ?>"
         <?php elseif($bgFooter = theme_option('footer_background_image')): ?>
             data-background="<?php echo e(RvMedia::getImageUrl($bgFooter)); ?>"
         <?php endif; ?>

         style="
            background-color: <?php echo e(Theme::get('footerBackgroundColor') ?: theme_option('footer_background_color', '#F8F8F8')); ?>;
            --footer-text-color: <?php echo e(Theme::get('footerTextColor') ?: theme_option('footer_text_color', '#000000')); ?>;
            --footer-text-muted-color: <?php echo e(Theme::get('footerTextMutedColor') ?: theme_option('footer_text_muted_color', '#777777')); ?>;
            --footer-border-color: <?php echo e(Theme::get('footerBorderColor') ?: theme_option('footer_border_color', '#E0E0E0')); ?>;
        "
    >
        <div class="footer-top-two">
            <div class="container">
                <div class="row">
                    <?php if(Theme::get('footerStyle', 'default') == 'default'): ?>
                        <?php echo dynamic_sidebar('footer_sidebar'); ?>

                    <?php else: ?>
                        <?php echo dynamic_sidebar('footer_sidebar_style_1'); ?>

                    <?php endif; ?>
                </div>
            </div>
        </div>

        
        <?php if(theme_option('google_map_enabled', true)): ?>
            <div class="google-map-section">
                <div class="container-fluid p-0">
                    <div class="contact-map" style="height: <?php echo e(theme_option('google_map_height', '400')); ?>px;">
                        <?php if(theme_option('google_map_address')): ?>
                            <iframe
                                src="https://www.google.com/maps?q=<?php echo e(urlencode(theme_option('google_map_address'))); ?>&output=embed"
                                style="width: 100%; height: 100%; border: 0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade"
                                title="Google Maps - <?php echo e(theme_option('google_map_address')); ?>">
                            </iframe>
                        <?php else: ?>
                            <div class="map-placeholder d-flex align-items-center justify-content-center"
                                 style="width: 100%; height: 100%; background-color: #f8f9fa; color: #6c757d;">
                                <div class="text-center">
                                    <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                                    <p class="mb-0"><?php echo e(__('Google Map will be displayed here')); ?></p>
                                    <small><?php echo e(__('Please enter your address in Theme Options')); ?></small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if(Theme::get('bottomFooterStyle', 'default') == 'default'): ?>
            <?php echo dynamic_sidebar('footer_bottom'); ?>

        <?php else: ?>
            <div class="footer-bottom container">
                <div class="row align-items-center">
                    <?php echo dynamic_sidebar('footer_bottom_style_1'); ?>

                </div>
            </div>
        <?php endif; ?>
    </div>
</footer>


<?php echo Theme::partial('floating-contact'); ?>


<?php echo Theme::footer(); ?>

<?php /**PATH C:\laragon\www\dtc-business-company\platform\themes/gerow/partials/footer.blade.php ENDPATH**/ ?>