# Hướng dẫn cài đặt Google Map trong Footer

## Tổng quan
Tôi đã thêm thành công Google Map vào phần footer của website với các tính năng sau:
- Hiển thị bản đồ Google Maps bằng iframe (không cần API key)
- Responsive design cho mobile
- Cấu hình đơn giản qua Theme Options
- Tương thích với cách Google Map đã có sẵn trong contact page

## Các file đã được tạo/chỉnh sửa:

### 1. Template Files:
- `platform/themes/gerow/partials/footer.blade.php` - Thêm HTML cho Google Map

### 2. Assets:
- `platform/themes/gerow/assets/sass/google-map.scss` - CSS styling cho map

### 3. Configuration:
- `platform/themes/gerow/functions/theme-options.php` - Thêm các options cấu hình
- `platform/themes/gerow/config.php` - Load CSS file
- `platform/themes/gerow/webpack.mix.js` - Compile assets

## Cách cấu hình:

### Bước 1: <PERSON><PERSON><PERSON> hình trong Admin Panel
1. Đ<PERSON>ng nhập vào Admin Panel
2. Vào **Appearance > Theme Options**
3. Tìm section **Google Map**
4. Cấu hình các thông tin sau:

#### Cài đặt:
- **Enable Google Map in Footer**: Bật/tắt hiển thị map
- **Address**: Nhập địa chỉ đầy đủ (VD: "123 Đường ABC, Quận XYZ, Hà Nội, Việt Nam")
- **Map Height**: Chiều cao map tính bằng pixel (mặc định: 400px)

## Ví dụ cấu hình:

### Cho văn phòng tại Hà Nội:
```
Enable Google Map: Bật
Address: 123 Đường Láng, Đống Đa, Hà Nội, Việt Nam
Height: 400px
```

### Cho cửa hàng tại TP.HCM:
```
Enable Google Map: Bật
Address: 456 Đường Nguyễn Huệ, Quận 1, TP.HCM, Việt Nam
Height: 350px
```

### Cho địa điểm du lịch:
```
Enable Google Map: Bật
Address: Hồ Hoàn Kiếm, Hoàn Kiếm, Hà Nội, Việt Nam
Height: 500px
```

## Tính năng đã implement:

### 1. Responsive Design:
- Desktop: Hiển thị đầy đủ với chiều cao tùy chỉnh
- Tablet: Tự động giảm chiều cao xuống 300px
- Mobile: Tối ưu với chiều cao 250px

### 2. Fallback khi chưa cấu hình:
- Hiển thị placeholder với icon và thông báo
- Hướng dẫn người dùng nhập địa chỉ

### 3. Styling:
- Bo góc và shadow đẹp mắt
- Dark theme support
- Tích hợp với design system của theme

### 4. Performance:
- Sử dụng iframe thay vì JavaScript API
- Không cần API key
- Loading lazy cho iframe

## Troubleshooting:

### Map không hiển thị:
1. Kiểm tra địa chỉ có được nhập đúng không
2. Thử địa chỉ đơn giản hơn (bỏ số nhà, chỉ giữ tên đường và thành phố)
3. Kiểm tra kết nối internet
4. Mở Developer Tools để xem lỗi iframe

### Map hiển thị sai vị trí:
1. Nhập địa chỉ chi tiết hơn
2. Thêm tên thành phố và quốc gia
3. Sử dụng tên địa danh nổi tiếng gần đó

### Map bị chặn:
1. Kiểm tra firewall hoặc ad blocker
2. Thử truy cập trực tiếp Google Maps để test

## Customization:

### Thay đổi style map:
Có thể chỉnh sửa CSS trong file `google-map.scss` để thay đổi:
- Border radius
- Box shadow
- Responsive breakpoints
- Dark theme colors

### Thêm multiple locations:
Hiện tại chỉ hỗ trợ 1 địa điểm. Để hiển thị nhiều địa điểm, có thể:
1. Tạo thêm theme options cho địa chỉ thứ 2, thứ 3
2. Sử dụng shortcode Google Map trong content thay vì footer

### Thay đổi vị trí hiển thị:
Map hiện tại hiển thị trong footer. Có thể di chuyển code HTML sang:
- Header
- Sidebar
- Contact page
- Bất kỳ template nào khác

## Hỗ trợ:
Nếu gặp vấn đề, hãy kiểm tra:
1. Theme Options có được lưu đúng không
2. Address có format đúng không
3. CSS có được load không (F12 > Network tab)

---
**Ưu điểm**: Không cần API key, dễ setup, tương thích tốt với Google Maps embed.
