# Hướng dẫn cài đặt Google Map trong Footer

## Tổng quan
Tôi đã thêm thành công Google Map vào phần footer của website với các tính năng sau:
- Hi<PERSON><PERSON> thị bản đồ Google Maps tương tác
- Marker tùy chỉnh với thông tin địa điểm
- Info window với nội dung HTML
- Responsive design cho mobile
- Cấu hình đầy đủ qua Theme Options

## Các file đã được tạo/chỉnh sửa:

### 1. Template Files:
- `platform/themes/gerow/partials/footer.blade.php` - Thêm HTML cho Google Map
- `platform/themes/gerow/partials/header-meta.blade.php` - Thêm Google Maps API script

### 2. Assets:
- `platform/themes/gerow/assets/js/google-map.js` - JavaScript xử lý Google Map
- `platform/themes/gerow/assets/sass/google-map.scss` - CSS styling cho map

### 3. Configuration:
- `platform/themes/gerow/functions/theme-options.php` - Thêm các options cấu hình
- `platform/themes/gerow/config.php` - Load CSS file
- `platform/themes/gerow/webpack.mix.js` - Compile assets

## Cách cấu hình:

### Bước 1: Lấy Google Maps API Key
1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Bật Google Maps JavaScript API
4. Tạo API Key và hạn chế domain của bạn
5. Copy API Key để sử dụng

### Bước 2: Cấu hình trong Admin Panel
1. Đăng nhập vào Admin Panel
2. Vào **Appearance > Theme Options**
3. Tìm section **Google Map**
4. Cấu hình các thông tin sau:

#### Cài đặt cơ bản:
- **Enable Google Map in Footer**: Bật/tắt hiển thị map
- **Google Maps API Key**: Dán API Key từ bước 1
- **Latitude**: Tọa độ vĩ độ (VD: 21.0285 cho Hà Nội)
- **Longitude**: Tọa độ kinh độ (VD: 105.8542 cho Hà Nội)
- **Map Zoom Level**: Mức zoom (1-20, khuyến nghị: 15)
- **Map Height**: Chiều cao map tính bằng pixel (khuyến nghị: 400px)

#### Cài đặt nâng cao:
- **Map Type**: Chọn loại bản đồ (Roadmap, Satellite, Hybrid, Terrain)
- **Marker Title**: Tiêu đề cho marker
- **Custom Marker Icon**: Upload icon tùy chỉnh cho marker
- **Info Window Content**: Nội dung HTML hiển thị khi click marker
- **Auto Open Info Window**: Tự động mở info window khi load trang

### Bước 3: Lấy tọa độ địa điểm
1. Truy cập [Google Maps](https://maps.google.com)
2. Tìm địa điểm của bạn
3. Click chuột phải và chọn tọa độ để copy
4. Hoặc sử dụng các công cụ online để convert địa chỉ thành tọa độ

## Ví dụ cấu hình:

### Cho văn phòng tại Hà Nội:
```
API Key: YOUR_GOOGLE_MAPS_API_KEY
Latitude: 21.0285
Longitude: 105.8542
Zoom: 15
Height: 400px
Map Type: roadmap
Marker Title: Văn phòng chính
Info Window: <h4>Công ty ABC</h4><p>123 Đường XYZ, Hà Nội</p><p>📞 024.1234.5678</p>
```

### Cho cửa hàng tại TP.HCM:
```
API Key: YOUR_GOOGLE_MAPS_API_KEY
Latitude: 10.8231
Longitude: 106.6297
Zoom: 16
Height: 350px
Map Type: roadmap
Marker Title: Cửa hàng TP.HCM
Info Window: <h4>Cửa hàng ABC</h4><p>456 Đường DEF, TP.HCM</p><p>📞 028.9876.5432</p>
```

## Tính năng đã implement:

### 1. Responsive Design:
- Desktop: Hiển thị đầy đủ tính năng
- Tablet: Tối ưu kích thước và controls
- Mobile: Giảm chiều cao, tối ưu touch

### 2. Fallback khi chưa cấu hình:
- Hiển thị placeholder với icon và thông báo
- Hướng dẫn người dùng cấu hình

### 3. Error Handling:
- Kiểm tra Google Maps API có load không
- Retry mechanism nếu API chưa sẵn sàng
- Console warnings cho debugging

### 4. Performance:
- Lazy loading Google Maps API
- Chỉ load khi cần thiết
- Minified assets

## Troubleshooting:

### Map không hiển thị:
1. Kiểm tra API Key có đúng không
2. Kiểm tra domain có được whitelist trong Google Cloud Console
3. Kiểm tra tọa độ có hợp lệ không
4. Mở Developer Tools để xem lỗi JavaScript

### Map hiển thị nhưng không có marker:
1. Kiểm tra tọa độ latitude/longitude
2. Kiểm tra zoom level (không quá cao hoặc quá thấp)

### Info window không hiển thị:
1. Kiểm tra HTML content có hợp lệ không
2. Kiểm tra setting "Auto Open Info Window"

## Customization:

### Thay đổi style map:
Có thể thêm custom styles JSON vào theme options để thay đổi màu sắc, ẩn/hiện elements của map.

### Thêm multiple markers:
Có thể mở rộng JavaScript để hỗ trợ nhiều marker bằng cách modify file `google-map.js`.

### Thay đổi vị trí hiển thị:
Map hiện tại hiển thị trong footer. Có thể di chuyển code HTML sang vị trí khác trong template.

## Hỗ trợ:
Nếu gặp vấn đề, hãy kiểm tra:
1. Console log trong Developer Tools
2. Network tab để xem API calls
3. Google Cloud Console để xem API usage và errors

---
**Lưu ý**: Nhớ giữ bí mật API Key và không commit vào version control công khai.
