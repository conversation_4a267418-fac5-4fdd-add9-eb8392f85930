
<?php if(theme_option('floating_contact_enabled', true)): ?>
    <div class="floating-contact-widget">
        
        <?php if($phone = theme_option('floating_contact_phone')): ?>
            <div class="floating-contact-item phone-contact">
                <a href="tel:<?php echo e($phone); ?>" class="contact-link" title="<?php echo e(__('Call us')); ?>">
                    <i class="fas fa-phone"></i>
                </a>
                <div class="contact-tooltip">
                    <span class="tooltip-label"><?php echo e(__('Hotline')); ?></span>
                    <span class="tooltip-value"><?php echo e($phone); ?></span>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($zalo = theme_option('floating_contact_zalo')): ?>
            <div class="floating-contact-item zalo-contact">
                <a href="https://zalo.me/<?php echo e($zalo); ?>" target="_blank" class="contact-link" title="<?php echo e(__('Chat on Zalo')); ?>">
                    <i class="fab fa-zalo"></i>
                </a>
                <div class="contact-tooltip">
                    <span class="tooltip-label"><?php echo e(__('Zalo')); ?></span>
                    <span class="tooltip-value"><?php echo e($zalo); ?></span>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($viber = theme_option('floating_contact_viber')): ?>
            <div class="floating-contact-item viber-contact">
                <a href="viber://chat?number=<?php echo e($viber); ?>" class="contact-link" title="<?php echo e(__('Chat on Viber')); ?>">
                    <i class="fab fa-viber"></i>
                </a>
                <div class="contact-tooltip">
                    <span class="tooltip-label"><?php echo e(__('Viber')); ?></span>
                    <span class="tooltip-value"><?php echo e($viber); ?></span>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($facebook = theme_option('floating_contact_facebook')): ?>
            <div class="floating-contact-item facebook-contact">
                <a href="<?php echo e($facebook); ?>" target="_blank" class="contact-link" title="<?php echo e(__('Facebook')); ?>">
                    <i class="fab fa-facebook-f"></i>
                </a>
                <div class="contact-tooltip">
                    <span class="tooltip-label"><?php echo e(__('Facebook')); ?></span>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($tiktok = theme_option('floating_contact_tiktok')): ?>
            <div class="floating-contact-item tiktok-contact">
                <a href="<?php echo e($tiktok); ?>" target="_blank" class="contact-link" title="<?php echo e(__('TikTok')); ?>">
                    <i class="fab fa-tiktok"></i>
                </a>
                <div class="contact-tooltip">
                    <span class="tooltip-label"><?php echo e(__('TikTok')); ?></span>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if($youtube = theme_option('floating_contact_youtube')): ?>
            <div class="floating-contact-item youtube-contact">
                <a href="<?php echo e($youtube); ?>" target="_blank" class="contact-link" title="<?php echo e(__('YouTube')); ?>">
                    <i class="fab fa-youtube"></i>
                </a>
                <div class="contact-tooltip">
                    <span class="tooltip-label"><?php echo e(__('YouTube')); ?></span>
                </div>
            </div>
        <?php endif; ?>

        
        <?php if(theme_option('floating_contact_back_to_top', true)): ?>
            <div class="floating-contact-item back-to-top">
                <a href="#" class="contact-link back-to-top-btn" title="<?php echo e(__('Back to top')); ?>">
                    <i class="fas fa-chevron-up"></i>
                </a>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\laragon\www\dtc-business-company\platform\themes/gerow/partials/floating-contact.blade.php ENDPATH**/ ?>