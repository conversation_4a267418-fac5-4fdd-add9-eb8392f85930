/**
 * Google Map Footer Styles
 */

.google-map-section {
    position: relative;
    margin-top: 0;

    .contact-map {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

        iframe {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            border-radius: 8px;
        }

        .map-placeholder {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
                border-color: #adb5bd;
                background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            }

            i {
                color: #6c757d;
                margin-bottom: 1rem;
            }

            p {
                font-size: 1.1rem;
                font-weight: 500;
                color: #495057;
                margin-bottom: 0.5rem;
            }

            small {
                color: #6c757d;
                font-size: 0.875rem;
            }
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .google-map-section {
        .contact-map {
            height: 300px !important;

            .map-placeholder {
                padding: 2rem 1rem;

                i {
                    font-size: 2rem !important;
                }

                p {
                    font-size: 1rem;
                }

                small {
                    font-size: 0.8rem;
                }
            }
        }
    }
}

@media (max-width: 480px) {
    .google-map-section {
        .contact-map {
            height: 250px !important;

            .map-placeholder {
                padding: 1.5rem 0.5rem;

                i {
                    font-size: 1.5rem !important;
                    margin-bottom: 0.5rem;
                }

                p {
                    font-size: 0.9rem;
                    margin-bottom: 0.25rem;
                }

                small {
                    font-size: 0.75rem;
                }
            }
        }
    }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
    .google-map-section {
        .contact-map {
            .map-placeholder {
                background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                border-color: #4a5568;
                color: #e2e8f0;

                &:hover {
                    border-color: #718096;
                    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
                }

                i {
                    color: #a0aec0;
                }

                p {
                    color: #f7fafc;
                }

                small {
                    color: #a0aec0;
                }
            }
        }
    }
}
