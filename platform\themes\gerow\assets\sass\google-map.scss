/**
 * Google Map Footer Styles
 */

.google-map-section {
    position: relative;
    margin-top: 0;
    
    .google-map-container {
        position: relative;
        overflow: hidden;
        
        #google-map {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
        }
        
        .map-placeholder {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            transition: all 0.3s ease;
            
            &:hover {
                border-color: #adb5bd;
                background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            }
            
            i {
                color: #6c757d;
                margin-bottom: 1rem;
            }
            
            p {
                font-size: 1.1rem;
                font-weight: 500;
                color: #495057;
                margin-bottom: 0.5rem;
            }
            
            small {
                color: #6c757d;
                font-size: 0.875rem;
            }
        }
    }
    
    // Google Maps controls styling
    .gm-style {
        .gm-style-iw {
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            
            .gm-style-iw-d {
                overflow: hidden !important;
                max-height: none !important;
            }
            
            .gm-style-iw-t::after {
                background: white;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
        }
        
        .gm-style-iw-c {
            padding: 0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .gm-style-iw-tc {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }
    }
    
    // Custom map controls
    .map-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        
        .map-control-btn {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            margin-left: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            
            &:hover {
                background: #f8f9fa;
                border-color: #adb5bd;
            }
            
            &.active {
                background: var(--primary-color, #0055FF);
                color: white;
                border-color: var(--primary-color, #0055FF);
            }
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .google-map-section {
        .google-map-container {
            height: 300px !important;
            
            .map-placeholder {
                padding: 2rem 1rem;
                
                i {
                    font-size: 2rem !important;
                }
                
                p {
                    font-size: 1rem;
                }
                
                small {
                    font-size: 0.8rem;
                }
            }
        }
        
        .map-controls {
            top: 5px;
            right: 5px;
            
            .map-control-btn {
                padding: 6px 10px;
                font-size: 12px;
                margin-left: 3px;
            }
        }
    }
}

@media (max-width: 480px) {
    .google-map-section {
        .google-map-container {
            height: 250px !important;
            
            .map-placeholder {
                padding: 1.5rem 0.5rem;
                
                i {
                    font-size: 1.5rem !important;
                    margin-bottom: 0.5rem;
                }
                
                p {
                    font-size: 0.9rem;
                    margin-bottom: 0.25rem;
                }
                
                small {
                    font-size: 0.75rem;
                }
            }
        }
    }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
    .google-map-section {
        .google-map-container {
            .map-placeholder {
                background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                border-color: #4a5568;
                color: #e2e8f0;
                
                &:hover {
                    border-color: #718096;
                    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
                }
                
                i {
                    color: #a0aec0;
                }
                
                p {
                    color: #f7fafc;
                }
                
                small {
                    color: #a0aec0;
                }
            }
        }
        
        .map-controls {
            .map-control-btn {
                background: #2d3748;
                border-color: #4a5568;
                color: #e2e8f0;
                
                &:hover {
                    background: #4a5568;
                    border-color: #718096;
                }
            }
        }
    }
}

// Animation for map loading
.google-map-loading {
    .google-map-container {
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: loading-shimmer 1.5s infinite;
            z-index: 1;
        }
    }
}

@keyframes loading-shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}
