# Hướng dẫn cài đặt Floating Contact Widget

## Tổng quan
Tôi đã tạo thành công một widget liên hệ nổi (floating contact widget) giống như trong hình bạn yêu cầu với các tính năng sau:

- **Floating buttons** hiển thị bên phải màn hình
- **Responsive design** cho mobile/tablet/desktop
- **Smooth animations** và hover effects
- **Back to top** functionality
- **Analytics tracking** (Google Analytics, Facebook Pixel)
- **Customizable** qua Theme Options

## Các nút liên hệ bao gồm:

### 🔥 **Hotline** (màu cam với hiệu ứng pulse)
- Icon: Điện thoại
- Chức năng: Gọi điện trực tiếp
- Tooltip: Hiển thị số điện thoại

### 💬 **Zalo** (màu xanh dương)
- Icon: Z (custom icon)
- Chức năng: Mở chat Zalo
- Link: `https://zalo.me/[số điện thoại]`

### 📱 **Viber** (màu tím)
- Icon: Viber
- Chức năng: Mở chat Viber
- Link: `viber://chat?number=[số điện thoại]`

### 📘 **Facebook** (màu xanh Facebook)
- Icon: Facebook
- Chức năng: Mở trang Facebook
- Link: URL trang Facebook

### 🎵 **TikTok** (màu đen với gradient)
- Icon: TikTok
- Chức năng: Mở profile TikTok
- Link: URL profile TikTok

### 📺 **YouTube** (màu đỏ)
- Icon: YouTube
- Chức năng: Mở kênh YouTube
- Link: URL kênh YouTube

### ⬆️ **Back to Top** (màu hồng)
- Icon: Mũi tên lên
- Chức năng: Cuộn lên đầu trang
- Hiển thị: Chỉ khi scroll xuống 30% trang

## Files đã tạo:

### 1. Template:
- `platform/themes/gerow/partials/floating-contact.blade.php`

### 2. Assets:
- `platform/themes/gerow/assets/sass/floating-contact.scss` (5.43 KiB)
- `platform/themes/gerow/assets/js/floating-contact.js` (8.31 KiB)

### 3. Configuration:
- `platform/themes/gerow/functions/theme-options.php` (thêm section Floating Contact)
- `platform/themes/gerow/config.php` (load CSS/JS)
- `platform/themes/gerow/webpack.mix.js` (compile assets)

## Cách cấu hình:

### Bước 1: Vào Admin Panel
1. Đăng nhập Admin Panel
2. Vào **Appearance > Theme Options**
3. Tìm section **Floating Contact**

### Bước 2: Cấu hình các options:

#### ✅ **Enable Floating Contact Widget**
- Bật/tắt hiển thị widget

#### 📞 **Phone Number**
- Nhập số điện thoại hotline
- VD: `0903172969`

#### 💬 **Zalo Number**
- Nhập số Zalo (thường giống số điện thoại)
- VD: `0903172969`

#### 📱 **Viber Number**
- Nhập số Viber
- VD: `0903172969`

#### 📘 **Facebook URL**
- Nhập link trang Facebook
- VD: `https://facebook.com/yourpage`

#### 🎵 **TikTok URL**
- Nhập link profile TikTok
- VD: `https://tiktok.com/@yourusername`

#### 📺 **YouTube URL**
- Nhập link kênh YouTube
- VD: `https://youtube.com/c/yourchannel`

#### ⬆️ **Show Back to Top Button**
- Bật/tắt nút back to top

## Ví dụ cấu hình:

```
✅ Enable Floating Contact Widget: Bật
📞 Phone Number: 0903172969
💬 Zalo Number: 0903172969
📱 Viber Number: 0903172969
📘 Facebook URL: https://facebook.com/dtccompany
🎵 TikTok URL: https://tiktok.com/@dtccompany
📺 YouTube URL: https://youtube.com/c/dtccompany
⬆️ Show Back to Top Button: Bật
```

## Tính năng nâng cao:

### 🎨 **Responsive Design:**
- **Desktop**: 50px buttons, full tooltips
- **Tablet**: 45px buttons, smaller tooltips
- **Mobile**: 40px buttons, no tooltips

### ✨ **Animations:**
- **Slide in** từ phải khi load trang
- **Pulse effect** cho nút hotline
- **Hover effects** với scale và shadow
- **Smooth scroll** cho back to top

### 📊 **Analytics Tracking:**
- Tự động track clicks qua Google Analytics
- Hỗ trợ Facebook Pixel
- Console logging cho debugging

### 🎯 **Smart Positioning:**
- Tự động ẩn khi gần footer
- Responsive positioning
- Z-index cao để luôn hiển thị trên cùng

## Customization:

### Thay đổi màu sắc:
Chỉnh sửa file `floating-contact.scss` để thay đổi gradient colors cho từng nút.

### Thêm nút mới:
Sử dụng JavaScript API:
```javascript
window.floatingContactWidget.addContactItem({
    icon: 'fas fa-envelope',
    url: 'mailto:<EMAIL>',
    title: 'Email us',
    class: 'email-contact',
    style: { background: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)' }
});
```

### Ẩn/hiện widget:
```javascript
window.floatingContactWidget.hide(); // Ẩn
window.floatingContactWidget.show(); // Hiện
```

## Troubleshooting:

### Widget không hiển thị:
1. Kiểm tra Theme Options có bật không
2. Kiểm tra CSS có load không (F12 > Network)
3. Clear cache browser

### Nút không hoạt động:
1. Kiểm tra URL có đúng format không
2. Kiểm tra JavaScript console có lỗi không
3. Thử click trên desktop trước

### Responsive không đúng:
1. Kiểm tra viewport meta tag
2. Test trên device thật
3. Kiểm tra CSS media queries

---
**Kết quả**: Widget sẽ hiển thị bên phải màn hình với các nút liên hệ đẹp mắt, giống hệt như trong hình bạn yêu cầu!
