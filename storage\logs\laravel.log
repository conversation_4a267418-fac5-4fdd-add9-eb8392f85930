[2025-07-13 14:42:04] production.ERROR: Database file at path [C:\laragon\www\dtc-business-company\database\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): Database file at path [C:\\laragon\\www\\dtc-business-company\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. (Connection: sqlite, SQL: PRAGMA foreign_keys = ON;) at C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#1 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#2 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(560): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#3 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#4 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\laragon\\\\www\\\\...', '', Array)
#5 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(75): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\laragon\\\\www\\\\...', '', Array)
#6 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#7 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#8 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#9 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#10 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#11 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#12 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('db.schema', Array, true)
#13 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('db.schema', Array)
#14 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('db.schema', Array)
#15 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('db.schema')
#16 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(240): Illuminate\\Container\\Container->offsetGet('db.schema')
#17 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('db.schema')
#18 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#19 C:\\laragon\\www\\dtc-business-company\\platform\\core\\base\\src\\Providers\\BaseServiceProvider.php(94): Illuminate\\Support\\Facades\\Facade::__callStatic('defaultStringLe...', Array)
#20 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Botble\\Base\\Providers\\BaseServiceProvider->register()
#21 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Botble\\Base\\Providers\\BaseServiceProvider))
#22 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load(Array)
#23 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#24 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#27 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\dtc-business-company\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#29 {main}

[previous exception] [object] (Illuminate\\Database\\SQLiteDatabaseDoesNotExistException(code: 0): Database file at path [C:\\laragon\\www\\dtc-business-company\\database\\database.sqlite] does not exist. Ensure this is an absolute path to the database. at C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php:34)
[stacktrace]
#0 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(221): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#1 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#2 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1339): call_user_func(Object(Closure))
#3 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(581): Illuminate\\Database\\Connection->getPdo()
#4 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('PRAGMA foreign_...', Array)
#5 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('PRAGMA foreign_...', Array, Object(Closure))
#6 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('PRAGMA foreign_...', Array, Object(Closure))
#7 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(560): Illuminate\\Database\\Connection->statement('PRAGMA foreign_...')
#8 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\SQLiteConnection.php(36): Illuminate\\Database\\Schema\\Builder->enableForeignKeyConstraints()
#9 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(272): Illuminate\\Database\\SQLiteConnection->__construct(Object(Closure), 'C:\\\\laragon\\\\www\\\\...', '', Array)
#10 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(75): Illuminate\\Database\\Connectors\\ConnectionFactory->createConnection('sqlite', Object(Closure), 'C:\\\\laragon\\\\www\\\\...', '', Array)
#11 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(50): Illuminate\\Database\\Connectors\\ConnectionFactory->createSingleConnection(Array)
#12 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(175): Illuminate\\Database\\Connectors\\ConnectionFactory->make(Array, 'sqlite')
#13 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(101): Illuminate\\Database\\DatabaseManager->makeConnection('sqlite')
#14 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseServiceProvider.php(74): Illuminate\\Database\\DatabaseManager->connection()
#15 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Database\\DatabaseServiceProvider->Illuminate\\Database\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#16 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#17 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('db.schema', Array, true)
#18 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('db.schema', Array)
#19 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('db.schema', Array)
#20 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1454): Illuminate\\Foundation\\Application->make('db.schema')
#21 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(240): Illuminate\\Container\\Container->offsetGet('db.schema')
#22 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('db.schema')
#23 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(349): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#24 C:\\laragon\\www\\dtc-business-company\\platform\\core\\base\\src\\Providers\\BaseServiceProvider.php(94): Illuminate\\Support\\Facades\\Facade::__callStatic('defaultStringLe...', Array)
#25 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(817): Botble\\Base\\Providers\\BaseServiceProvider->register()
#26 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Botble\\Base\\Providers\\BaseServiceProvider))
#27 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(794): Illuminate\\Foundation\\ProviderRepository->load(Array)
#28 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#29 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(286): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#30 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#31 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#32 C:\\laragon\\www\\dtc-business-company\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\dtc-business-company\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#34 {main}
"} 
