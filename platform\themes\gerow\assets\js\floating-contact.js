/**
 * Floating Contact Widget JavaScript
 */

class FloatingContactWidget {
    constructor() {
        this.widget = document.querySelector('.floating-contact-widget');
        this.backToTopBtn = document.querySelector('.back-to-top-btn');
        this.init();
    }

    init() {
        if (!this.widget) return;

        this.setupBackToTop();
        this.setupScrollBehavior();
        this.setupClickTracking();
    }

    setupBackToTop() {
        if (!this.backToTopBtn) return;

        this.backToTopBtn.addEventListener('click', (e) => {
            e.preventDefault();
            this.scrollToTop();
        });
    }

    setupScrollBehavior() {
        let lastScrollTop = 0;
        let isScrolling = false;

        window.addEventListener('scroll', () => {
            if (!isScrolling) {
                window.requestAnimationFrame(() => {
                    this.handleScroll();
                    isScrolling = false;
                });
                isScrolling = true;
            }
        });
    }

    handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const windowHeight = window.innerHeight;
        const documentHeight = document.documentElement.scrollHeight;

        // Show/hide back to top button
        if (this.backToTopBtn) {
            const backToTopItem = this.backToTopBtn.closest('.floating-contact-item');
            if (scrollTop > windowHeight * 0.3) {
                backToTopItem.style.opacity = '1';
                backToTopItem.style.visibility = 'visible';
            } else {
                backToTopItem.style.opacity = '0';
                backToTopItem.style.visibility = 'hidden';
            }
        }

        // Hide widget when near footer
        const footer = document.querySelector('footer');
        if (footer) {
            const footerRect = footer.getBoundingClientRect();
            const widgetHeight = this.widget.offsetHeight;
            
            if (footerRect.top < window.innerHeight && footerRect.top < widgetHeight + 100) {
                this.widget.style.transform = `translateY(calc(-50% - ${Math.max(0, widgetHeight + 100 - footerRect.top)}px))`;
            } else {
                this.widget.style.transform = 'translateY(-50%)';
            }
        }
    }

    scrollToTop() {
        const duration = 800;
        const start = window.pageYOffset;
        const startTime = performance.now();

        const animateScroll = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            
            window.scrollTo(0, start * (1 - easeOut));

            if (progress < 1) {
                requestAnimationFrame(animateScroll);
            }
        };

        requestAnimationFrame(animateScroll);
    }

    setupClickTracking() {
        // Track clicks for analytics (optional)
        const contactLinks = this.widget.querySelectorAll('.contact-link');
        
        contactLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const contactType = this.getContactType(link);
                this.trackContactClick(contactType);
            });
        });
    }

    getContactType(link) {
        const item = link.closest('.floating-contact-item');
        if (item.classList.contains('phone-contact')) return 'phone';
        if (item.classList.contains('zalo-contact')) return 'zalo';
        if (item.classList.contains('viber-contact')) return 'viber';
        if (item.classList.contains('facebook-contact')) return 'facebook';
        if (item.classList.contains('tiktok-contact')) return 'tiktok';
        if (item.classList.contains('youtube-contact')) return 'youtube';
        if (item.classList.contains('back-to-top')) return 'back-to-top';
        return 'unknown';
    }

    trackContactClick(type) {
        // Google Analytics tracking (if available)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'contact_click', {
                'contact_type': type,
                'event_category': 'floating_contact'
            });
        }

        // Facebook Pixel tracking (if available)
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Contact', {
                contact_type: type
            });
        }

        // Console log for debugging
        console.log('Floating contact clicked:', type);
    }

    // Public method to show/hide widget
    show() {
        if (this.widget) {
            this.widget.style.display = 'flex';
        }
    }

    hide() {
        if (this.widget) {
            this.widget.style.display = 'none';
        }
    }

    // Public method to add custom contact item
    addContactItem(config) {
        if (!this.widget || !config.icon || !config.url) return;

        const item = document.createElement('div');
        item.className = `floating-contact-item ${config.class || 'custom-contact'}`;
        
        item.innerHTML = `
            <a href="${config.url}" ${config.target ? `target="${config.target}"` : ''} 
               class="contact-link" title="${config.title || ''}">
                <i class="${config.icon}"></i>
            </a>
            ${config.tooltip ? `
                <div class="contact-tooltip">
                    <span class="tooltip-label">${config.tooltip.label || ''}</span>
                    ${config.tooltip.value ? `<span class="tooltip-value">${config.tooltip.value}</span>` : ''}
                </div>
            ` : ''}
        `;

        // Insert before back-to-top button if it exists
        const backToTop = this.widget.querySelector('.back-to-top');
        if (backToTop) {
            this.widget.insertBefore(item, backToTop);
        } else {
            this.widget.appendChild(item);
        }

        // Apply custom styles if provided
        if (config.style) {
            const link = item.querySelector('.contact-link');
            Object.assign(link.style, config.style);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.floatingContactWidget = new FloatingContactWidget();
});

// Initialize back-to-top visibility on load
window.addEventListener('load', function() {
    if (window.floatingContactWidget) {
        window.floatingContactWidget.handleScroll();
    }
});
