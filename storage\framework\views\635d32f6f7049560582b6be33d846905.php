<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <meta charset="utf-8">
    <meta
        http-equiv="X-UA-Compatible"
        content="IE=edge"
    >
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1"
    />
    <meta
        name="csrf-token"
        content="<?php echo e(csrf_token()); ?>"
    >

    <?php echo BaseHelper::googleFonts(
        'https://fonts.googleapis.com/css2?family=' .
            urlencode(theme_option('primary_font', 'Manrope')) .
            ':wght@200;300;400;500;600;700;800&display=swap',
    ); ?>

    <style>
        :root {
            --primary-color: <?php echo e($primaryColor = theme_option('primary_color', '#0055FF')); ?>;
            --primary-color-rgb: <?php echo e(implode(',', BaseHelper::hexToRgb($primaryColor))); ?>;
            --primary-hover-color: <?php echo e(theme_option('primary_hover_color', '#hover')); ?>;
            --secondary-color: <?php echo e($secondaryColor = theme_option('secondary_color', '#00194C')); ?>;
            --secondary-color-rgb: <?php echo e(implode(',', BaseHelper::hexToRgb($secondaryColor))); ?>;
            --heading-color: <?php echo e(theme_option('heading_color', '#00194C')); ?>;
            --text-color: <?php echo e(theme_option('text_color', '#334770')); ?>;
            --heading-font: '<?php echo e(theme_option('heading-font', 'Plus Jakarta Sans')); ?>', sans-serif;
            --primary-font: '<?php echo e(theme_option('primary_font', 'Urbanist')); ?>', sans-serif;
        }
    </style>

    <?php echo Theme::header(); ?>


    
    <?php if(theme_option('google_map_enabled', true) && theme_option('google_map_api_key')): ?>
        <script>
            window.themeOptions = window.themeOptions || {};
            window.themeOptions.googleMapLatitude = <?php echo e(theme_option('google_map_latitude', 21.0285)); ?>;
            window.themeOptions.googleMapLongitude = <?php echo e(theme_option('google_map_longitude', 105.8542)); ?>;
            window.themeOptions.googleMapZoom = <?php echo e(theme_option('google_map_zoom', 15)); ?>;
            window.themeOptions.googleMapType = '<?php echo e(theme_option('google_map_type', 'roadmap')); ?>';
            window.themeOptions.googleMapMarkerTitle = '<?php echo e(theme_option('google_map_marker_title', 'Our Location')); ?>';
            <?php if(theme_option('google_map_marker_icon')): ?>
                window.themeOptions.googleMapMarkerIcon = '<?php echo e(RvMedia::getImageUrl(theme_option('google_map_marker_icon'))); ?>';
            <?php endif; ?>
            <?php if(theme_option('google_map_info_window')): ?>
                window.themeOptions.googleMapInfoWindow = <?php echo json_encode(theme_option('google_map_info_window')); ?>;
            <?php endif; ?>
            window.themeOptions.googleMapAutoOpenInfo = <?php echo e(theme_option('google_map_auto_open_info', false) ? 'true' : 'false'); ?>;
            <?php if(theme_option('google_map_custom_styles')): ?>
                window.themeOptions.googleMapCustomStyles = <?php echo json_encode(theme_option('google_map_custom_styles')); ?>;
            <?php endif; ?>
        </script>
        <script async defer src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(theme_option('google_map_api_key')); ?>&callback=initGoogleMapFooter"></script>
    <?php endif; ?>

    <?php if(theme_option('animation_enabled', 'yes') !== 'yes'): ?>
        <style>
            .img-reveal {
                visibility: visible;
            }
        </style>
    <?php endif; ?>
</head>

<?php
    Theme::addBodyAttributes(['class' => 'body-header-' . Theme::get('headerStyle')]);
?>

<body <?php echo Theme::bodyAttributes(); ?>>
<?php /**PATH C:\laragon\www\dtc-business-company\platform\themes/gerow/partials/header-meta.blade.php ENDPATH**/ ?>