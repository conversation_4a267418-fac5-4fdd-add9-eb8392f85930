<section class="inner-contact-area pt-80 pb-80">
    <div class="container">
        <div class="row align-items-center">
            <?php if($image = $shortcode->image): ?>
                <div class="col-lg-6">
                    <div class="inner-contact-img">
                        <?php echo e(RvMedia::image($image, $shortcode->title)); ?>

                    </div>
                </div>
            <?php endif; ?>
            <div class="col-lg-6">
                <div class="inner-contact-info">
                    <?php if($title = $shortcode->title): ?>
                        <h2 class="title"><?php echo e($title); ?></h2>
                    <?php endif; ?>

                    <?php $__currentLoopData = $tabs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tab): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="contact-info-item">
                            <h5 class="title-two"><?php echo e($tab['title']); ?></h5>
                            <ul class="list-wrap">
                                <li>
                                    <?php echo BaseHelper::clean($tab['address']); ?>

                                </li>
                                <li dir="ltr"><?php echo e($tab['phone']); ?></li>
                                <li dir="ltr"><?php echo e($tab['email']); ?></li>
                            </ul>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\laragon\www\dtc-business-company\platform\themes/gerow/partials/shortcodes/branch-offices/index.blade.php ENDPATH**/ ?>