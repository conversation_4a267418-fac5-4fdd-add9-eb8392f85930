<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta
        http-equiv="X-UA-Compatible"
        content="IE=edge"
    >
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1"
    />
    <meta
        name="csrf-token"
        content="{{ csrf_token() }}"
    >

    {!! BaseHelper::googleFonts(
        'https://fonts.googleapis.com/css2?family=' .
            urlencode(theme_option('primary_font', 'Manrope')) .
            ':wght@200;300;400;500;600;700;800&display=swap',
    ) !!}
    <style>
        :root {
            --primary-color: {{ $primaryColor = theme_option('primary_color', '#0055FF') }};
            --primary-color-rgb: {{ implode(',', BaseHelper::hexToRgb($primaryColor)) }};
            --primary-hover-color: {{ theme_option('primary_hover_color', '#hover') }};
            --secondary-color: {{ $secondaryColor = theme_option('secondary_color', '#00194C') }};
            --secondary-color-rgb: {{ implode(',', BaseHelper::hexToRgb($secondaryColor)) }};
            --heading-color: {{ theme_option('heading_color', '#00194C') }};
            --text-color: {{ theme_option('text_color', '#334770') }};
            --heading-font: '{{ theme_option('heading-font', 'Plus Jakarta Sans') }}', sans-serif;
            --primary-font: '{{ theme_option('primary_font', 'Urbanist') }}', sans-serif;
        }
    </style>

    {!! Theme::header() !!}

    {{-- Google Maps API --}}
    @if (theme_option('google_map_enabled', true) && theme_option('google_map_api_key'))
        <script>
            window.themeOptions = window.themeOptions || {};
            window.themeOptions.googleMapLatitude = {{ theme_option('google_map_latitude', 21.0285) }};
            window.themeOptions.googleMapLongitude = {{ theme_option('google_map_longitude', 105.8542) }};
            window.themeOptions.googleMapZoom = {{ theme_option('google_map_zoom', 15) }};
            window.themeOptions.googleMapType = '{{ theme_option('google_map_type', 'roadmap') }}';
            window.themeOptions.googleMapMarkerTitle = '{{ theme_option('google_map_marker_title', 'Our Location') }}';
            @if (theme_option('google_map_marker_icon'))
                window.themeOptions.googleMapMarkerIcon = '{{ RvMedia::getImageUrl(theme_option('google_map_marker_icon')) }}';
            @endif
            @if (theme_option('google_map_info_window'))
                window.themeOptions.googleMapInfoWindow = {!! json_encode(theme_option('google_map_info_window')) !!};
            @endif
            window.themeOptions.googleMapAutoOpenInfo = {{ theme_option('google_map_auto_open_info', false) ? 'true' : 'false' }};
            @if (theme_option('google_map_custom_styles'))
                window.themeOptions.googleMapCustomStyles = {!! json_encode(theme_option('google_map_custom_styles')) !!};
            @endif
        </script>
        <script async defer src="https://maps.googleapis.com/maps/api/js?key={{ theme_option('google_map_api_key') }}&callback=initGoogleMapFooter"></script>
    @endif

    @if (theme_option('animation_enabled', 'yes') !== 'yes')
        <style>
            .img-reveal {
                visibility: visible;
            }
        </style>
    @endif
</head>

@php
    Theme::addBodyAttributes(['class' => 'body-header-' . Theme::get('headerStyle')]);
@endphp

<body {!! Theme::bodyAttributes() !!}>
